#!/bin/bash

echo "🔧 Starting full frontend setup & cleanup..."

FRONTEND_DIR="frontend"

### 1. Check Node.js & npm installation
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (v18+)."
    exit 1
fi
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm (comes with Node.js)."
    exit 1
fi
echo "✅ Node.js $(node -v) and npm $(npm -v) detected."

### 2. Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ $FRONTEND_DIR directory not found."
    exit 1
fi

cd $FRONTEND_DIR

### 3. Install dependencies if package.json exists
if [ -f "package.json" ]; then
    echo "📦 Installing/updating dependencies..."
    npm install
else
    echo "⚠️ No package.json found. Skipping dependency install."
fi

### 4. Fix vulnerabilities and outdated packages
if [ -f "package.json" ]; then
    echo "🛠️ Running npm audit fix..."
    npm audit fix || echo "⚠️ npm audit fix completed with warnings."

    echo "📜 Checking outdated packages..."
    npm outdated || true
fi

### 5. <PERSON> Prettier and E<PERSON>int (auto-fix)
if [ -f "package.json" ]; then
    echo "✨ Running Prettier & ESLint cleanup..."
    npx prettier --write "src/**/*.{js,css,html}" >/dev/null 2>&1 || echo "⚠️ Prettier not found."
    npx eslint --fix "src/**/*.{js,jsx}" >/dev/null 2>&1 || echo "⚠️ ESLint not found."
fi

### 6. Remove unused imports with eslint-plugin-unused-imports (if installed)
if [ -f "package.json" ]; then
    echo "🧹 Removing unused imports (if supported)..."
    npx eslint --fix "src/**/*.{js,jsx}" --rule "unused-imports/no-unused-imports: error" >/dev/null 2>&1 || true
fi

### 7. Optimize images (optional)
if command -v npx &> /dev/null; then
    echo "🖼️ Optimizing images (if imagemin installed)..."
    npx imagemin "public/*.{jpg,png}" --out-dir=public >/dev/null 2>&1 || true
fi

### 8. Verify build
if [ -f "package.json" ]; then
    echo "🏗️ Verifying build..."
    npm run build || { echo "❌ Build failed! Check errors above."; exit 1; }
fi

echo "🎉 Frontend setup & cleanup complete!"
