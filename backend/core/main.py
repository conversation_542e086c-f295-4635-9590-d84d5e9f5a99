# Alsania Memory Control Plane - Main Application

# Standard library imports
import asyncio, logging, os, json
from pathlib import Path
from contextlib import asynccontextmanager
from datetime import datetime

# Third-party imports
import requests
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.params import Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, field_validator

# Add the backend directory to Python path for absolute imports
import sys
from pathlib import Path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Local imports - Configuration and Core Utilities
from config.config import config

# Local imports - Memory Subsystem
from memory.storage import bulk_store, save_snapshot, load_snapshot, list_snapshots, delete_snapshot
from memory.forensics.forensics import log_edit, log_access
from lib.secure_memory_id import secure_memory_id
from memory.vector_store import VectorStore
from memory.snapshots.core.integrity_check import generate_hashes

# Local imports - Infrastructure
from infra.scripts.embedding.embedding import embed_text, get_embedding

# Local imports - Agent System
from agents.core.agent_manager import list_agents
from agents.scribe.scribe_agent import ScribeAgent
from agents.sentinel.sentinel import start_sentinel

# Local imports - API Layer
from api.routes import router as routes
from api.metrics import router as metrics

# Local imports - Experimental/Chaos
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
try:
    from data.chaos.chaos_mcp import start_chaos_mode, start_chaos_agent
except ImportError:
    # Fallback functions if chaos module is not available
    def start_chaos_mode():
        logger.warning("Chaos mode not available - module not found")
        pass

    def start_chaos_agent():
        logger.warning("Chaos agent not available - module not found")
        pass

# === Global Variables ===
BASE_DIR = Path(__file__).resolve().parent
vector_store = VectorStore()

# Configure logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL.upper()))
logger = logging.getLogger("alsaniamcp")

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("🧠 Starting Alsania Memory Control Plane")
    config.print_config()
    if not config.validate():
        logger.warning("⚠️ Configuration validation failed")

    if config.ENABLE_SENTINEL:
        logger.info("🛡️ Starting Sentinel")
        start_sentinel()
    if config.ENABLE_CHAOS_MODE:
        logger.info("🔥 Starting Chaos Mode")
        start_chaos_mode()
    start_chaos_agent()

    logger.info("✅ Application startup complete")
    yield
    logger.info("🛑 Shutting down application")

app = FastAPI(
    title="AlsaniaMCP",
    description="Hardened memory server for the Alsania ecosystem",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Add middleware and routes
app.include_router(metrics)
app.include_router(routes)

# Mount frontend
frontend_path = Path(__file__).parent.parent.parent / "frontend"
if frontend_path.exists():
    app.mount("/frontend", StaticFiles(directory=str(frontend_path)), name="frontend")

# Security configuration
security = HTTPBearer()

def verify_token(token: str = Depends(security)):
    """Verify API token for protected endpoints."""
    if not token or token.credentials != config.API_TOKEN:
        raise HTTPException(status_code=401, detail="Invalid token")
    return token

# Initialize vector database
vector_db = vector_store
vector_db.ensure_collection()

# Initialize snapshots directory and generate hashes if possible
try:
    snapshots_dir = Path("snapshots")
    snapshots_dir.mkdir(exist_ok=True)
    generate_hashes()
except Exception as e:
    logger.warning(f"Could not initialize snapshots: {e}")

# === Pydantic Models ===
class QueryRequest(BaseModel):
    """Request model for query operations."""
    query: str
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Query cannot be empty")
        if len(v) > 10000:
            raise ValueError("Query too long")
        return v.strip()

class StoreRequest(BaseModel):
    """Request model for storing memory entries."""
    text: str
    source: str = "api_user"
    
    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Memory text cannot be empty")
        return v.strip()

# === Helper Functions ===
def save_memory_entry(mem_id: str, text: str, source: str):
    """Save a memory entry to storage."""
    # This function should be implemented based on your storage requirements
    # For now, we'll use the bulk_store function with a single entry
    memory = {
        "mem_id": mem_id,
        "text": text,
        "source": source,
        "metadata": {"agent_access": ["echo"]}
    }
    bulk_store([memory])

def search_similar(embedding, top_k: int = 5, namespace: str = "default"):
    """Search for similar vectors using the vector store."""
    return vector_store.search(embedding, top_k, namespace)

def insert_vector(embedding, payload, namespace: str = "default"):
    """Insert a vector into the vector store."""
    text = payload.get("text", "")
    return vector_store.insert(text, embedding, namespace)

# === API Endpoints ===


# Note: @app.on_event("startup") is deprecated in favor of lifespan context manager
# The startup logic is now handled in the lifespan function above

# === Scribe Agent Endpoints ===
@app.get("/scribe/get_chapters")
async def get_chapters():
    """Get chapters from Scribe agent."""
    if "scribe" not in list_agents():
        # Even if Scribe isn't running, we can return chapters
        agent = ScribeAgent(vector_store)
        return {"chapters": agent.get_chapters()}

    return {"chapters": list_agents()["scribe"].get_chapters()}

@app.post("/scribe/write")
async def write_chapter(request: Request):
    """Trigger Scribe agent to write a new chapter."""
    context = (await request.json()).get("context", "No context")
    # If scribe is running, push a task to its queue
    vector_store.push_task("scribe_mem", context)
    return {"status": "triggered"}

@app.post("/scribe/edit_chapter")
async def edit_chapter(request: Request):
    """Edit an existing chapter in Scribe agent."""
    data = await request.json()
    index = int(data.get("index", -1))
    content = data.get("content")

    agent = ScribeAgent(vector_store)
    try:
        agent.edit_chapter(index, content)
    except IndexError:
        raise HTTPException(status_code=400, detail="Invalid chapter index")

    return {"status": f"Chapter {index + 1} updated"}

# === Main Application Endpoints ===
@app.get("/")
async def root():
    """Root endpoint - serve frontend or return status."""
    frontend_file = Path(__file__).parent.parent.parent / "frontend" / "mcp.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    return {"message": "AlsaniaMCP is running", "version": "1.0.0"}

@app.get("/api")
def api_root():
    """API root endpoint."""
    return {"message": "Echo is alive and hardened.", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "ok",
        "qdrant": "connected",
        "postgres": "not_used",
        "ipfs": "ready",
        "vector_collection": getattr(vector_store, 'collection', 'default')
    }

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint."""
    return {"status": "ready"}

# === Core Memory Operations ===
@app.post("/ask")
async def ask(request: QueryRequest, _: str = Depends(verify_token)):
    """Query the memory system."""
    try:
        query = request.query
        logger.info(f"📨 Received query: {query}")
        embedding = get_embedding(query)
        if embedding is None:
            raise HTTPException(status_code=500, detail="Failed to generate embedding")
        results = search_similar(embedding)
        mem_id = secure_memory_id()
        log_access(mem_id, "query")
        return {"results": results, "mem_id": mem_id, "query": query}
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail="Failed to process query")

@app.post("/store")
async def store_memory(data: StoreRequest, _: str = Depends(verify_token)):
    """Store a new memory entry."""
    try:
        embedding = get_embedding(data.text)
        if embedding is None:
            raise HTTPException(status_code=500, detail="Failed to generate embedding")
        mem_id = secure_memory_id()
        save_memory_entry(mem_id, data.text, data.source)
        payload = {"memory_id": mem_id, "text": data.text, "source": data.source}
        insert_vector(embedding, payload)
        log_edit(mem_id, data.source)
        return {"status": "ok", "mem_id": mem_id}
    except Exception as e:
        logger.error(f"Error storing memory: {e}")
        raise HTTPException(status_code=500, detail="Failed to store memory")

@app.post("/vector/search")
async def search_vector(query: QueryRequest):
    """Search vectors using simple embedding."""
    embedding = embed_text(query.query)
    results = vector_db.search(embedding) if hasattr(vector_db, 'search') else []
    return results

# === Snapshot Management ===
@app.post("/snapshot/save")
async def save_snapshot_endpoint():
    """Save a new snapshot."""
    dummy_data = {"msg": "Sample data snapshot"}
    snapshot_id = save_snapshot(dummy_data)
    return {"id": snapshot_id}

@app.get("/snapshot/list")
async def list_snapshots_endpoint():
    """List all available snapshots."""
    return list_snapshots()

@app.get("/snapshot/load/{snapshot_id}")
async def load_snapshot_endpoint(snapshot_id: str):
    """Load a specific snapshot."""
    try:
        return load_snapshot(snapshot_id)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Snapshot not found")

@app.delete("/snapshot/delete/{snapshot_id}")
async def delete_snapshot_endpoint(snapshot_id: str):
    """Delete a specific snapshot."""
    try:
        delete_snapshot(snapshot_id)
        return {"status": "deleted", "id": snapshot_id}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Snapshot not found")

# === IPFS Integration ===
def upload_to_ipfs(file_path: str, api_url: str = "http://127.0.0.1:5001/api/v0/add") -> str:
    """Upload a file to IPFS and return the hash."""
    try:
        with open(file_path, "rb") as file:
            response = requests.post(api_url, files={"file": file})
        if response.status_code == 200:
            return response.json()["Hash"]
        else:
            raise Exception(f"IPFS upload failed: {response.text}")
    except Exception as e:
        logger.error(f"IPFS upload error: {e}")
        raise

@app.get("/snapshot/export/{snapshot_id}")
async def export_snapshot_to_ipfs(snapshot_id: str):
    """Export a snapshot to IPFS."""
    try:
        data = load_snapshot(snapshot_id)
        file_path = f"snapshots/{snapshot_id}.json"
        
        # Ensure the file exists before uploading
        if not os.path.exists(file_path):
            # Create the file if it doesn't exist
            with open(file_path, "w") as f:
                json.dump(data, f, indent=2)
        
        ipfs_cid = upload_to_ipfs(file_path)

        cidmap_path = "snapshots/cidmap.json"
        if os.path.exists(cidmap_path):
            with open(cidmap_path, "r") as f:
                cidmap = json.load(f)
        else:
            cidmap = {}

        cidmap[snapshot_id] = {
            "cid": ipfs_cid,
            "timestamp": datetime.now().isoformat(),
            "url": f"https://ipfs.io/ipfs/{ipfs_cid}"
        }

        with open(cidmap_path, "w") as f:
            json.dump(cidmap, f, indent=2)

        return cidmap[snapshot_id]
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Snapshot not found")
    except Exception as e:
        logger.error(f"Export to IPFS failed: {e}")
        raise HTTPException(status_code=500, detail="Export failed")

# === Utility Endpoints ===
@app.get("/stream")
async def stream_test():
    """Test streaming endpoint."""
    async def event_generator():
        for i in range(10):
            yield f"data: Echo #{i}\n\n"
            await asyncio.sleep(1)
    return StreamingResponse(event_generator(), media_type="text/event-stream")

# === Application Entry Point ===
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.RELOAD,
        log_level=config.LOG_LEVEL.lower()
    )