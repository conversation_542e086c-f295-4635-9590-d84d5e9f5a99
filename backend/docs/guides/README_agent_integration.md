# Agent Integration in Alsania MCP

## Overview

The Alsania MCP supports multiple agents (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>) running alongside the core FastAPI app. This document explains how they fit together and how to add more agents.

---

## Current Agents

### 1. **Echo Agent**
- Primary architect AI for Alsania
- Lives in `src/agents/echo_agent.py`
- Uses memory namespace: `echo_core`

### 2. **Scribe Agent**
- Writes **The Book of Alsania**: a narrative history of all activities
- Config file: `src/agents/scribe-agent-config.json`
- Output: `logs/book-of-alsania.md`
- Integrated directly into `main.py` via FastAPI startup event

### 3. **Future Agents (Cypher, etc.)**
- Will use `agent-template.json` and `agent-spawner.py` to register

---

## Adding New Agents

1. Create `<new_agent>.py` in `src/agents/`
2. Create `<new_agent>-config.json` in the same folder
3. Add a background task in `main.py`:
   ```python
   from src.agents import new_agent
   asyncio.create_task(new_agent.start())
   ```
4. Configure the agent in its `*-config.json` file
5. Test and iterate on your agent's functionality within the context of the existing system.
6. Once stable, consider integrating it with other parts of the application as needed.