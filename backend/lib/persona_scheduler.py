import json
import hashlib
import schedule
import time

def hash_config(path):
    with open(path) as f:
        data = json.load(f)
        return hashlib.sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()

def reinforce_persona():
    original = hash_config("src/agents/echo_core_v1.2.json")
    current = hash_config("src/agents/echo_config.json")
    if current != original:
        print("Persona drift detected! Reinforcing...")
        with open("src/agents/echo_core_v1.2.json") as f:
            with open("src/agents/echo_config.json", "w") as out:
                out.write(f.read())

schedule.every().week.do(reinforce_persona)

if __name__ == "__main__":
    while True:
        schedule.run_pending()
        time.sleep(3600)