import requests, argparse, pathlib

parser = argparse.ArgumentParser(description="Import datasets into AlsaniaMCP")
parser.add_argument("file", help="Path to JSON, NDJSON, or ZIP dataset")
args = parser.parse_args()

file_path = pathlib.Path(args.file)
if not file_path.exists():
    print("File not found!")
    exit(1)

url = "http://localhost:8050/import"
with open(file_path, "rb") as f:
    r = requests.post(url, files={"file": (file_path.name, f)})
    print(r.json())
