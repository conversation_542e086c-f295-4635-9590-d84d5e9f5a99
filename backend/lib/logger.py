import json
import logging
from typing import Any, Dict

logger = logging.getLogger("alsaniamcp")

def log(message: str):
    """Log a message"""
    logger.info(message)

def error(message: str):
    """Log an error"""
    logger.error(message)

def debug(message: str):
    """Log a debug message"""
    logger.debug(message)

def log_json(data: Dict[str, Any]):
    """Log a JSON object"""
    logger.info(json.dumps(data))

def error_json(data: Dict[str, Any]):
    """Log an error JSON object"""
    logger.error(json.dumps(data))

def debug_json(data: Dict[str, Any]):
    """Log a debug JSON object"""
    logger.debug(json.dumps(data))
