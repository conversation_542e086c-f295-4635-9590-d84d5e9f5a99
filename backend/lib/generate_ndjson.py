import zipfile, re, json
from pathlib import Path
from datetime import datetime

zip_path = Path("chatshitoryv1.zip")
out_path = Path("chat_history_clean.ndjson")
audio_pattern = re.compile(r"<<Audio.*?>>|<<Image.*?>>|<<.*?Displayed>>", re.IGNORECASE)

memories = []
with zipfile.ZipFile(zip_path, "r") as zip_ref:
    zip_ref.extractall("chat_history_tmp")
    for f in Path("chat_history_tmp").glob("**/*"):
        if f.suffix.lower() in [".txt", ".json", ".log"]:
            lines = f.read_text(encoding="utf-8", errors="ignore").splitlines()
            for line in lines:
                line = audio_pattern.sub("", line).strip()
                if line:
                    speaker, text = ("Unknown", line)
                    if ":" in line and len(line.split(":")[0]) < 25:
                        speaker, text = line.split(":", 1)
                    memories.append({
                        "text": text.strip(),
                        "source": "chat_history",
                        "metadata": {
                            "speaker": speaker.strip(),
                            "timestamp": datetime.utcnow().isoformat(),
                            "agent_access": ["echo"]
                        }
                    })

with open(out_path, "w", encoding="utf-8") as out_f:
    for m in memories:
        out_f.write(json.dumps(m) + "\n")

print(f"Saved {len(memories)} entries to {out_path}")
