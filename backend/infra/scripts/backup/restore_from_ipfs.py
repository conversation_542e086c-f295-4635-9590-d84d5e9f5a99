import json
import os
import requests
import subprocess

SNAPSHOT_LOG = "snapshots/log.json"
DOWNLOAD_DIR = "snapshots/downloads"
RESTORE_DIR = "snapshots/restore"

os.makedirs(DOWNLOAD_DIR, exist_ok=True)
os.makedirs(RESTORE_DIR, exist_ok=True)

def download_from_ipfs(cid, filename):
    url = f"https://ipfs.io/ipfs/{cid}"
    response = requests.get(url)
    if response.status_code == 200:
        filepath = os.path.join(DOWNLOAD_DIR, filename)
        with open(filepath, "wb") as f:
            f.write(response.content)
        return filepath
    else:
        raise Exception(f"Failed to download from IPFS: {url} ({response.status_code})")

def restore():
    with open(SNAPSHOT_LOG, "r") as f:
        entries = json.load(f)

    if not entries:
        print("No snapshot entries found.")
        return

    latest = entries[-1]
    cid = latest["cid"]
    file = latest["file"]

    print(f"Restoring snapshot {file} from IPFS CID {cid}...")

    filepath = download_from_ipfs(cid, file)

    if file.endswith(".sql"):
        subprocess.run(["psql", "-U", "postgres", "-d", "mem0", "-f", filepath])
    elif file.endswith(".tar.gz"):
        subprocess.run(["tar", "-xzvf", filepath, "-C", RESTORE_DIR])
        print("Qdrant vector data extracted. You may need to restart the Qdrant container.")

if __name__ == "__main__":
    restore()