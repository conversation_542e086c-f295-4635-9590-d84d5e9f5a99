from apscheduler.schedulers.background import BackgroundScheduler
from memory.snapshots.core.integrity_check import generate_hashes
from backup_to_ipfs import run_backup
from memory.snapshots.core.prune_snapshots import prune_old_snapshots

BACKUP_INTERVAL_MINUTES = 60  # Every hour
SNAPSHOT_DIR = "snapshots"
MAX_SNAPSHOTS = 5

def scheduled_backup():
    print("⏰ Scheduled backup triggered.")
    generate_hashes()
    run_backup()
    prune_old_snapshots(SNAPSHOT_DIR, MAX_SNAPSHOTS)

def start_scheduler():
    scheduler = BackgroundScheduler()
    scheduler.add_job(scheduled_backup, "interval", minutes=BACKUP_INTERVAL_MINUTES)
    scheduler.start()
    print(f"🛡 Backup scheduler started: every {BACKUP_INTERVAL_MINUTES} minutes.")

if __name__ == "__main__":
    start_scheduler()