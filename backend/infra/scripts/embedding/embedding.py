import os
import requests
from typing import List
import pickle
import hashlib
from tenacity import retry, stop_after_attempt, wait_exponential

def load_embedding_from_cache(text_hash):
    """Load embedding from cache for given text hash."""
    if not os.path.exists("cache"):
        os.makedirs("cache")
    with open(f"cache/{text_hash}.emb", "rb") as f:
        print(f"Loading embedding from cache for hash: {text_hash}")
        emb = pickle.load(f)
        return emb

def store_embedding_in_cache(text_hash, emb):
    """Store embedding in cache for given text hash."""
    with open(f"cache/{text_hash}.emb", "wb") as f:
        pickle.dump(emb, f)

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def embed_text(text: str) -> List[float]:
    """Simple embedding function that converts text to numerical representation."""
    return [float(ord(c) % 10) for c in text[:64]]

def get_embedding(text):
    """Get embedding for text using cache or OpenAI API."""
    text_hash = hashlib.sha256(text.encode()).hexdigest()

    if os.path.exists(f"cache/{text_hash}.emb"):
        return load_embedding_from_cache(text_hash)

    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        raise ValueError("OPENROUTER_API_KEY not set")
    
    model = os.getenv("OPENROUTER_MODEL", "mistralai/mistral-7b-instruct")
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    try:
        res = requests.post("https://openrouter.ai/api/v1/embeddings", headers=headers, json={
            "model": model,
            "input": [text]
        })
        res.raise_for_status()
        embedding = res.json()["data"][0]["embedding"]
        
        # Store the embedding in cache
        store_embedding_in_cache(text_hash, embedding)
        return embedding
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
        raise
    except Exception as e:
        print("Embedding error:", e)
        return None
