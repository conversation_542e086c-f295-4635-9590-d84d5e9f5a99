# yaml-language-server: $schema=https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json
version: "3.9"

services:
  backend:
    build:
      context: ../../..                             # project root
      dockerfile: backend/infra/docker/backend.Dockerfile
    container_name: alsaniamcp-backend
    ports:
      - "8050:8050"
    environment:
      - HOST=0.0.0.0
      - PORT=8050
      - API_TOKEN=alsania-dev
      - QDRANT_URL=http://qdrant:6333
      - POSTGRES_URL=********************************************/mem0
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=mem0pass
      - POSTGRES_DB=mem0
      - LOG_LEVEL=INFO
      - ENABLE_SENTINEL=true
      - ENABLE_CHAOS_MODE=false

    depends_on:
      qdrant:
        condition: service_started
      postgres:
        condition: service_healthy
    volumes:
      - ../../../backend:/app

  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: alsaniamcp-qdrant
    volumes:
      - qdrant_storage:/qdrant/storage
    ports:
      - "6333:6333"
      - "6334:6334"
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:6333/readyz | grep -q 'ready'"]
      interval: 10s
      timeout: 5s
      retries: 10

  postgres:
    image: postgres:16-alpine
    container_name: alsaniamcp-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mem0pass
      POSTGRES_DB: mem0
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  frontend:
    image: nginx:alpine
    container_name: alsaniamcp-frontend
    ports:
      - "8080:8080"
    volumes:
      - ../../../frontend:/usr/share/nginx/html:ro
    depends_on:
      backend:
        condition: service_started

volumes:
  postgres_data:
  qdrant_storage:
  ollama_data:
