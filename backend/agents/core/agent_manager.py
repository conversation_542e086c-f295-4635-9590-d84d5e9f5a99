from threading import Thread
from lib.logger import log

# === Active agent registry ===
_agents = {}

def spawn_agent(name: str, agent_instance):
    """
    Spawns an agent in its own thread.
    """
    if name in _agents:
        log(f"⚠️ Agent '{name}' is already running.")
        return

    def run():
        log(f"🚀 Starting agent: {name}")
        try:
            agent_instance.run()
        except Exception as e:
            log(f"💥 Agent '{name}' crashed: {e}")
        finally:
            _agents.pop(name, None)
            log(f"🛑 Agent '{name}' stopped.")

    thread = Thread(target=run, daemon=True)
    _agents[name] = thread
    thread.start()

def stop_agent(name: str):
    """
    Stops an agent gracefully if it has a stop method.
    """
    if name not in _agents:
        log(f"⚠️ Agent '{name}' is not running.")
        return

    agent_instance = getattr(_agents[name], 'agent', None)
    if agent_instance and hasattr(agent_instance, 'stop'):
        agent_instance.stop()

    _agents.pop(name, None)
    log(f"🛑 Requested stop for agent: {name}")

def list_agents():
    """
    Returns the list of currently active agents.
    """
    return list(_agents.keys())
