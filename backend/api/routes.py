from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))
from memory.storage import bulk_store
import subprocess
import json, hashlib, tempfile, shutil, zipfile
from pathlib import Path
from starlette.responses import JSONResponse

# === Memory API ===
router = APIRouter(prefix="/api", tags=["Echo Memory"])

# Simple in-memory store for testing
memory_store = []

class Memory(BaseModel):
    text: str
    tag: str = None
    timestamp: str = None

@router.get("/")
def ping():
    return {"message": "Echo memory API is online."}

@router.post("/add")
def add_memory(mem: Memory):
    memory_store.append(mem)
    return {"status": "success", "stored": mem}

@router.get("/all")
def get_memories():
    return memory_store

@router.post("/snapshot")
def trigger_snapshot():
    try:
        result = subprocess.run(["python3", "infra/scripts/backup/backup_to_ipfs.py"], capture_output=True, text=True)
        if result.returncode == 0:
            return {"status": "success", "message": "Snapshot saved to IPFS"}
        else:
            return {"status": "error", "stderr": result.stderr}
    except Exception as e:
        return {"status": "failed", "error": str(e)}
    

# === Dataset Import ===
@router.post("/import")
async def import_dataset(file: UploadFile = File(...)):
    """
    Import a dataset (NDJSON, JSON, or ZIP of these) into the memory DB.
    All memories default to agent_access=["echo"] unless specified.
    """
    try:
        tmp_dir = Path(tempfile.mkdtemp())
        file_path = tmp_dir / file.filename

        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        extracted_files = []
        if file_path.suffix == ".zip":
            with zipfile.ZipFile(file_path, "r") as zip_ref:
                zip_ref.extractall(tmp_dir)
                extracted_files = list(tmp_dir.glob("**/*"))
        else:
            extracted_files = [file_path]

        imported_count = 0
        for f in extracted_files:
            if f.suffix in [".json", ".ndjson"]:
                with open(f, "r", encoding="utf-8") as src:
                    data = [json.loads(line) for line in src]

                memories = []
                for entry in data:
                    if not entry.get("text"):
                        continue
                    mem_id = hashlib.blake2b(entry["text"].encode(), digest_size=8).hexdigest()
                    memories.append({
                        "mem_id": mem_id,
                        "text": entry["text"],
                        "source": entry.get("source", "chat_history"),
                        "metadata": {
                            **entry.get("metadata", {}),
                            "agent_access": entry.get("metadata", {}).get("agent_access", ["echo"])
                        }
                    })
                bulk_store(memories)
                imported_count += len(memories)

        return JSONResponse({"status": "success", "imported": imported_count})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Import failed: {str(e)}")
