from fastapi import APIRouter
import psutil
import os
import json

router = APIRouter(prefix="/metrics", tags=["System Metrics"])

@router.get("/health")
def health_check():
    return {"status": "running", "pid": os.getpid()}

@router.get("/cpu")
def cpu_usage():
    return {"cpu_percent": psutil.cpu_percent(interval=1)}

@router.get("/memory")
def memory_usage():
    mem = psutil.virtual_memory()
    return {"total": mem.total, "used": mem.used, "percent": mem.percent}

@router.get("/disk")
def disk_usage():
    disk = psutil.disk_usage('/')
    return {"total": disk.total, "used": disk.used, "percent": disk.percent}

@router.get("/quarantine_log")
def quarantine_log():
    try:
        with open("logs/forensics.log", "r") as f:
            lines = f.readlines()[-20:]
            return {"entries": [json.loads(line) for line in lines if '"quarantine"' in line]}
    except Exception as e:
        return {"error": str(e)}