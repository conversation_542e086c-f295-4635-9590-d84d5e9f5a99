
import os, sys, importlib.util

def scan_imports(directory="backend"):
    print(f"🔍 Scanning {directory} for broken imports...")
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".py"):
                path = os.path.join(root, file)
                try:
                    spec = importlib.util.spec_from_file_location("temp", path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                except Exception as e:
                    print(f"❌ Import error in {path}: {e}")
    print("✅ Scan complete.")

if __name__ == "__main__":
    scan_imports()
