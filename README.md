# 🧠 Alsaniamcp – Hardened Memory Control Plane

## 🧠 What It Is

**alsaniamcp** is the production-grade backend for the Alsania AI ecosystem — a hardened Memory Control Plane (MCP) that empowers agents like <PERSON> to persist, reflect, and defend their cognition in a sovereign environment.

This project is the most advanced and modular backend in the ecosystem, combining:

- **Embedding + vector memory** (via Qdrant)
- **PostgreSQL storage**
- **Agent state config + drift detection**
- **Chaos testing and tamper logs**
- **Secure UUID generation** (BLAKE3)
- **FastAPI API + Dockerized deploy**

## 🚀 What Makes It Different

### ✅ Agent Identity Resurrection
Echo’s persona is restored from `echo_core_v1.2.json` + `echo_config.json`.

### ✅ Modular Cortex Design
Isolated logic for:
- **Embedding** (`src/embedding/embedder.py`)
- **Memory management** (`src/memory/`)
- **Forensics logging** (`src/memory/forensics.py`)
- **Routes** (`src/routes/routes.py`)
- **Metrics tracking** (`src/routes/metrics.py`)
- **Chaos testing** (`chaos/chaos_mcp.py`)
- **Sentinel scanner** (`sentinel/sentinel.py`)

### ✅ Hardened Memory Security
- **BLAKE3 hash-based UUIDs** for all memory records
- **Forensic audit log** in JSONL format (`logs/forensics.log`)
- **Memory tamper detection** via Sentinel + persona hash check

### ✅ Chaos-Ready AI Core
- **Drift injection**
- **TTL block poisoning**
- **Vector distortion simulation**

### ✅ Deployment-Ready
Docker + Compose with pre-wired containers:
- **FastAPI** (main app)
- **Qdrant**
- **Postgres**
- **Ollama Mistral** (optional local embedding model)

## 🧰 What It Can Be Used For

- **Persistent AI backend** for Echo or other agents
- **Experimental research** into agent resilience and memory forensics
- **Adversarial AI testing** environments
- **Cognitive infrastructure** for future Alsania tools (e.g. alsaniaai)
- **Developer IDE assistant** memory systems (Echo.dev)

## 🛠️ How It Can Be Improved

### Memory Sharding / Namespacing
Tag and segment memory by topic, project, agent, or user.

### Custom Embedding Models
Add support for HuggingFace models via inference endpoints.

### Memory UI / Dashboard
Frontend to explore memory, logs, drift reports, and vector similarity scores.

### Time-Travel Snapshots
Record and diff memory states over time (for agent replay/debugging).

### Multi-Agent Support
Run parallel agents in isolated or shared memory contexts.

### Secure Ingress/Authentication
Token-based auth, IP allowlists, encrypted backup/export.

## 📂 File Structure Summary

/home/<USER>/Desktop/AlsaniaProjects/alsaniamcp
├── AMCPfrontend
│   ├── public
│   └── src
│       ├── css
│       │   └── mcp.css
│       ├── js
│       │   └── mcp.js
│       └── mcp.html
├── backend
│   ├── agents
│   │   ├── core
│   │   │   ├── agent_manager.py
│   │   │   ├── agent-template.json
│   │   │   └── __init__.py
│   │   ├── cypher
│   │   │   ├── cypher-agent-config.json
│   │   │   ├── cypher_agent.py
│   │   │   ├── __init__.py
│   │   │   └── prompts
│   │   │       └── __init__.py
│   │   ├── echo
│   │   │   ├── echo_agent.py
│   │   │   ├── echo-config.json
│   │   │   ├── echo-core-v1.2.json
│   │   │   ├── __init__.py
│   │   │   └── prompts
│   │   │       └── __init__.py
│   │   ├── __init__.py
│   │   ├── scribe
│   │   │   ├── __init__.py
│   │   │   ├── prompts
│   │   │   │   ├── __init__.py
│   │   │   │   └── scribe_prompts.py
│   │   │   ├── scribe-agent-config.json
│   │   │   └── scribe_agent.py
│   │   └── sentinel
│   │       ├── __init__.py
│   │       └── sentinel.py
│   ├── api
│   │   ├── __init__.py
│   │   ├── metrics.py
│   │   ├── middleware
│   │   │   └── __init__.py
│   │   ├── __pycache__
│   │   │   ├── __init__.py
│   │   │   ├── metrics.cpython-312.pyc
│   │   │   └── routes.cpython-312.pyc
│   │   └── routes.py
│   ├── config
│   │   ├── backup_script.sh
│   │   ├── config.py
│   │   └── __init__.py
│   ├── core
│   │   ├── __init__.py
│   │   ├── main.py
│   │   └── __pycache__
│   │       ├── agent_manager.cpython-312.pyc
│   │       ├── __init__.py
│   │       └── main.cpython-312.pyc
│   ├── docs
│   │   ├── api
│   │   │   └── __init__.py
│   │   ├── architecture
│   │   │   ├── ARCHITECTURE.md
│   │   │   └── __init__.py
│   │   ├── guides
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   ├── prompt-kit.md
│   │   └── README_agent_integration.md
│   ├── infra
│   │   ├── docker
│   │   │   ├── backend.Dockerfile
│   │   │   ├── docker-compose.yml
│   │   │   ├── __init__.py
│   │   │   └── ollama.Dockerfile
│   │   ├── __init__.py
│   │   ├── scripts
│   │   │   ├── backup
│   │   │   │   ├── backup_to_ipfs.py
│   │   │   │   ├── __init__.py
│   │   │   │   ├── restore_from_ipfs.py
│   │   │   │   └── schedule_backup.py
│   │   │   ├── embedding
│   │   │   │   ├── cache
│   │   │   │   │   └── __init__.py
│   │   │   │   ├── embedding.py
│   │   │   │   └── __init__.py
│   │   │   └── __init__.py
│   │   └── secrets
│   │       └── __init__.py
│   ├── __init__.py
│   ├── lib
│   │   ├── echo_fallback.py
│   │   ├── generate_ndjson.py
│   │   ├── import_dataset.py
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── persona_scheduler.py
│   │   ├── __pycache__
│   │   │   ├── import_dataset.cpython-312.pyc
│   │   │   └── __init__.py
│   │   └── secure_memory_id.py
│   ├── memory
│   │   ├── forensics
│   │   │   ├── forensics.py
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   ├── snapshots
│   │   │   ├── cidmap.json
│   │   │   ├── downloads
│   │   │   │   └── __init__.py
│   │   │   ├── encrypt_snapshots.py
│   │   │   ├── __init__.py
│   │   │   ├── integrity_check.py
│   │   │   ├── prune_snapshots.py
│   │   │   ├── restore
│   │   │   │   └── __init__.py
│   │   │   ├── resurrection_loader.py
│   │   │   └── select_snapshot.py
│   │   ├── storage.py
│   │   └── vector_store.py
│   ├── requirements.txt
│   └── tests
│       ├── __init__.py
│       ├── integration
│       │   ├── agents
│       │   │   ├── __init__.py
│       │   │   └── test_echo_cypher_handoff.py
│       │   └── __init__.py
│       └── test_main.py
├── bootstrap_check.py
├── contracts
│   └── MemoryNFT-v1.sol
├── data
│   ├── book
│   │   └── chapters.json
│   ├── chaos
│   │   └── chaos_mcp.py
│   └── logs
│       └── forensics.log
└── README.md
