#!/bin/bash

echo "🔧 Starting full backend setup with auto-fixes..."

### 1. Verify critical files exist and create defaults if missing
echo "📂 Checking critical files..."
[ ! -f ".gitignore" ] && echo -e "__pycache__/\n.env\n*.pyc\n.DS_Store\nnode_modules/\ndata/\n" > .gitignore && echo "✅ Created default .gitignore"
[ ! -f "README.md" ] && echo "# AlsaniaMCP" > README.md && echo "✅ Created default README.md"
[ ! -f "backend/requirements.txt" ] && echo "fastapi\nuvicorn\npsycopg2\nqdrant-client\npython-dotenv" > backend/requirements.txt && echo "✅ Created default requirements.txt"
[ ! -f ".env.example" ] && echo "API_TOKEN=alsania-dev\nPOSTGRES_URL=********************************************/mem0\nQDRANT_URL=http://qdrant:6334" > .env.example && echo "✅ Created .env.example"
[ ! -f ".env" ] && cp .env.example .env && echo "✅ Created .env from example"

### 2. Add __init__.py to all backend subfolders
echo "📦 Adding __init__.py to all backend directories..."
find backend -type d ! -path "*/__pycache__/*" -exec sh -c 'touch "$0/__init__.py"' {} \;

### 3. Update requirements.txt (remove duplicates and sort)
echo "📜 Cleaning up requirements.txt..."
if [ -f "backend/requirements.txt" ]; then
    sort backend/requirements.txt | uniq > backend/requirements.txt.tmp
    mv backend/requirements.txt.tmp backend/requirements.txt
    echo "✅ Updated backend/requirements.txt"
fi

### 4. Check for broken imports and auto-install missing packages
echo "🔍 Scanning for broken imports..."
python3 - <<'EOF'
import os, importlib.util, subprocess, sys

errors = []
missing_modules = set()

for root, _, files in os.walk("backend"):
    for f in files:
        if f.endswith(".py"):
            path = os.path.join(root, f)
            try:
                spec = importlib.util.spec_from_file_location("temp", path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            except ModuleNotFoundError as e:
                missing_modules.add(e.name)
                errors.append(f"{path}: Missing module {e.name}")
            except Exception as e:
                errors.append(f"{path}: {e}")

# Auto-install missing modules
if missing_modules:
    print(f"⚠️ Installing missing modules: {', '.join(missing_modules)}")
    subprocess.run([sys.executable, "-m", "pip", "install", *missing_modules], check=False)

if errors:
    print("❌ Broken imports or issues detected:")
    for err in errors:
        print(f"  - {err}")
else:
    print("✅ No broken imports found!")
EOF

### 5. Auto-fix unused imports (requires autoflake)
if ! command -v autoflake &> /dev/null; then
    echo "📦 Installing autoflake for import cleanup..."
    pip install autoflake
fi

echo "🧹 Removing unused imports..."
autoflake --in-place --remove-all-unused-imports --recursive backend

### 6. Finish
echo "🎉 Backend setup and cleanup complete!"
