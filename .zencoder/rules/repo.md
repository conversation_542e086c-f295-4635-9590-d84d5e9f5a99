---
description: Repository Information Overview
alwaysApply: true
---

# <PERSON><PERSON>iamcp Information

## Summary
Alsaniamcp is a production-grade backend for the Alsania AI ecosystem — a hardened Memory Control Plane (MCP) that empowers AI agents to persist, reflect, and defend their cognition in a sovereign environment. It combines embedding and vector memory via Qdrant, PostgreSQL storage, agent state configuration, drift detection, chaos testing, and secure UUID generation.

## Structure
- **src/**: Core application modules (agents, embedding, memory, routes)
- **AMCPfrontend/**: Web UI for the MCP
- **chaos/**: Chaos testing framework for system resilience
- **contracts/**: Blockchain memory contracts
- **docs/**: Project documentation
- **logs/**: Audit trail and forensics logs
- **mem0_config/**: Memory backup utilities
- **ollama_mistral/**: Local embedding model configuration
- **tests/**: Test files for the application

## Language & Runtime
**Language**: Python
**Version**: 3.11 (based on Dockerfile)
**Build System**: Python setuptools
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- fastapi==0.104.1 - Web framework
- uvicorn[standard]==0.24.0 - ASGI server
- qdrant-client==1.7.0 - Vector database client
- psycopg2-binary==2.9.9 - PostgreSQL adapter
- blake3==0.4.1 - Cryptographic hashing
- pydantic==2.5.0 - Data validation
- numpy==1.24.4 - Numerical computing
- scikit-learn==1.3.2 - Machine learning utilities

## Build & Installation
```bash
# Option 1: Docker Compose (Recommended)
docker-compose up -d

# Option 2: Local Development
pip install -r requirements.txt
python main.py
```

## Docker
**Dockerfile**: Dockerfile
**Image**: Python 3.11-slim
**Configuration**: Multi-container setup with:
- Main application (FastAPI)
- Qdrant vector database
- PostgreSQL database
- Ollama Mistral embedding model

**Docker Compose**: docker-compose.yml defines services:
- mem0: Main application
- qdrant: Vector database
- postgres: Relational database
- ollama: Local embedding model

## Testing
**Framework**: Python's built-in unittest with FastAPI TestClient
**Test Location**: test_main.py and tests/ directory
**Naming Convention**: test_*.py
**Run Command**:
```bash
python -m pytest test_main.py -v
```

## API Endpoints
**Main Endpoints**:
- `/ask`: Query the memory system
- `/store`: Store new memory entries
- `/import`: Import datasets
- `/snapshot/save`: Create memory snapshots
- `/snapshot/load/{snapshot_id}`: Load memory snapshots
- `/metrics/health`: System health check

## Configuration
**Environment Variables**:
- `API_TOKEN`: Authentication token
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8050)
- `POSTGRES_URL`: PostgreSQL connection string
- `QDRANT_URL`: Qdrant vector database URL
- `OPENROUTER_API_KEY`: API key for embedding service
- `LOG_LEVEL`: Logging level (default: INFO)