// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

interface IMCP {
    function queryMemory(uint256 tokenId) external view returns (string memory);
}

contract MemoryNFT is ERC1155, Ownable {
    using Address for address;
    using SafeMath for uint256;

    struct TokenInfo {
        uint256 evolutionStage;
        uint256 lastEvolutionTime;
    }

    mapping(uint256 => TokenInfo) private _tokenInfo;

    address public mcp; // Address of MCP memory resolver
    string public baseURI;

    event Evolved(uint256 indexed tokenId, uint256 newStage);
    event MemoryLog(uint256 indexed tokenId, string action, uint256 timestamp);

    constructor(string memory _baseURI, address _mcp) ERC1155("") {
        baseURI = _baseURI;
        mcp = _mcp;
    }

    function setMCP(address _mcp) external onlyOwner {
        mcp = _mcp;
    }

    function setBaseURI(string memory _newBase) external onlyOwner {
        baseURI = _newBase;
    }

    function mint(address to, uint256 id, uint256 amount, bytes memory data) external onlyOwner {
        _mint(to, id, amount, data);
        _tokenInfo[id] = TokenInfo({
            evolutionStage: 0,
            lastEvolutionTime: block.timestamp
        });
        emit MemoryLog(id, "minted", block.timestamp);
    }

    function evolve(uint256 tokenId) external {
        require(balanceOf(msg.sender, tokenId) > 0, "You don't own this token");

        TokenInfo storage tokenInfo = _tokenInfo[tokenId];
        uint256 elapsedTime = block.timestamp.sub(tokenInfo.lastEvolutionTime);

        require(elapsedTime >= 1 days, "Too soon to evolve");

        tokenInfo.evolutionStage = tokenInfo.evolutionStage.add(1);
        tokenInfo.lastEvolutionTime = block.timestamp;

        emit Evolved(tokenId, tokenInfo.evolutionStage);
        emit MemoryLog(tokenId, "evolved", block.timestamp);
    }

    function uri(uint256 tokenId) public view override returns (string memory) {
        string memory memoryLog = IMCP(mcp).queryMemory(tokenId);
        return string(abi.encodePacked(baseURI, "?id=", toString(tokenId), "&log=", memoryLog));
    }

    function getStage(uint256 tokenId) external view returns (uint256) {
        return _tokenInfo[tokenId].evolutionStage;
    }

    function toString(uint256 value) internal pure returns (string memory) {
        if (value == 0) return "0";
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
}
