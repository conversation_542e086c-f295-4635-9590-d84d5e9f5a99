<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>AlsaniaMCP - Sovereign Memory Control Plane</title>
  <link rel="stylesheet" href="css/mcp.css"/>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script defer src="js/mcp.js"></script>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="alsania-logo">
        <i class="fas fa-brain"></i>
        <span>ALSANIA</span>
      </div>
      <div class="loading-text">Initializing Memory Control Plane...</div>
      <div class="loading-bar">
        <div class="loading-progress"></div>
      </div>
    </div>
  </div>

  <!-- Main Dashboard -->
  <div id="dashboard" class="dashboard hidden">
    <!-- Header -->
    <header class="header">
      <div class="header-left">
        <div class="alsania-brand">
          <i class="fas fa-brain brand-icon"></i>
          <div class="brand-text">
            <h1>ALSANIA<span class="mcp-text">MCP</span></h1>
            <p class="tagline">Sovereign Memory Control Plane</p>
          </div>
        </div>
      </div>
      <div class="header-center">
        <div class="system-time" id="system-time"></div>
        <div class="connection-status" id="connection-status">
          <i class="fas fa-wifi"></i>
          <span>Connected</span>
        </div>
      </div>
      <div class="header-right">
        <div class="echo-status" id="echo-status">
          <i class="fas fa-robot"></i>
          <span>Echo: <span id="echo-state">Initializing</span></span>
        </div>
      </div>
    </header>

    <!-- Navigation -->
    <nav class="nav-tabs">
      <button class="nav-tab active" data-tab="overview">
        <i class="fas fa-tachometer-alt"></i>
        Overview
      </button>
      <button class="nav-tab" data-tab="memory">
        <i class="fas fa-database"></i>
        Memory
      </button>
      <button class="nav-tab" data-tab="search">
        <i class="fas fa-search"></i>
        Search
      </button>
      <button class="nav-tab" data-tab="agent">
        <i class="fas fa-robot"></i>
        Agent
      </button>
      <button class="nav-tab" data-tab="scribe">
        <i class="fas fa-book"></i>
        Scribe
      </button>
      <button class="nav-tab" data-tab="security">
        <i class="fas fa-shield-alt"></i>
        Security
      </button>
      <button class="nav-tab" data-tab="system">
        <i class="fas fa-cogs"></i>
        System
      </button>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Overview Tab -->
      <div class="tab-content active" id="overview-tab">
        <div class="dashboard-grid">
          <!-- System Health Card -->
          <div class="card health-card">
            <div class="card-header">
              <h3><i class="fas fa-heartbeat"></i> System Health</h3>
              <div class="health-indicator" id="health-indicator">
                <div class="status-dot"></div>
                <span id="health-status-text">Checking...</span>
              </div>
            </div>
            <div class="card-content">
              <div class="health-metrics" id="health-metrics">
                <div class="metric">
                  <span class="metric-label">Qdrant</span>
                  <span class="metric-value" id="qdrant-status">—</span>
                </div>
                <div class="metric">
                  <span class="metric-label">PostgreSQL</span>
                  <span class="metric-value" id="postgres-status">—</span>
                </div>
                <div class="metric">
                  <span class="metric-label">Embedding</span>
                  <span class="metric-value" id="embedding-status">—</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Metrics Card -->
          <div class="card metrics-card">
            <div class="card-header">
              <h3><i class="fas fa-chart-line"></i> Performance</h3>
              <button class="refresh-btn" onclick="refreshMetrics()">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
            <div class="card-content">
              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-icon cpu">
                    <i class="fas fa-microchip"></i>
                  </div>
                  <div class="metric-info">
                    <span class="metric-label">CPU</span>
                    <span class="metric-value" id="cpu-usage">—%</span>
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-icon memory">
                    <i class="fas fa-memory"></i>
                  </div>
                  <div class="metric-info">
                    <span class="metric-label">Memory</span>
                    <span class="metric-value" id="memory-usage">—%</span>
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-icon disk">
                    <i class="fas fa-hdd"></i>
                  </div>
                  <div class="metric-info">
                    <span class="metric-label">Disk</span>
                    <span class="metric-value" id="disk-usage">—%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Echo Agent Status Card -->
          <div class="card agent-card">
            <div class="card-header">
              <h3><i class="fas fa-robot"></i> Echo Agent</h3>
              <div class="agent-indicator" id="agent-indicator">
                <div class="pulse-dot"></div>
                <span id="agent-status-text">Active</span>
              </div>
            </div>
            <div class="card-content">
              <div class="agent-stats">
                <div class="stat">
                  <span class="stat-label">Last Reflection</span>
                  <span class="stat-value" id="last-reflection">—</span>
                </div>
                <div class="stat">
                  <span class="stat-label">Memory Entries</span>
                  <span class="stat-value" id="memory-count">—</span>
                </div>
                <div class="stat">
                  <span class="stat-label">Uptime</span>
                  <span class="stat-value" id="agent-uptime">—</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity Card -->
          <div class="card activity-card">
            <div class="card-header">
              <h3><i class="fas fa-history"></i> Recent Activity</h3>
              <button class="clear-btn" onclick="clearActivity()">
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <div class="card-content">
              <div class="activity-feed" id="activity-feed">
                <div class="activity-item">
                  <div class="activity-icon">
                    <i class="fas fa-power-off"></i>
                  </div>
                  <div class="activity-content">
                    <span class="activity-text">System initialized</span>
                    <span class="activity-time">Just now</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Tab -->
      <div class="tab-content" id="memory-tab">
        <div class="memory-grid">
          <!-- Store Memory Card -->
          <div class="card store-card">
            <div class="card-header">
              <h3><i class="fas fa-plus-circle"></i> Store Memory</h3>
            </div>
            <div class="card-content">
              <form id="store-form" class="store-form">
                <div class="form-group">
                  <label for="memory-text">Memory Content</label>
                  <textarea id="memory-text" placeholder="Enter memory content..." rows="4" required></textarea>
                </div>
                <div class="form-group">
                  <label for="memory-source">Source</label>
                  <input type="text" id="memory-source" placeholder="e.g., user_input, system, agent" value="dashboard" required>
                </div>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save"></i>
                  Store Memory
                </button>
              </form>
            </div>
          </div>

          <!-- Memory Browser Card -->
          <div class="card browser-card">
            <div class="card-header">
              <h3><i class="fas fa-list"></i> Memory Browser</h3>
              <div class="browser-controls">
                <button class="btn btn-sm" onclick="refreshMemories()">
                  <i class="fas fa-sync-alt"></i>
                </button>
                <select id="memory-filter" onchange="filterMemories()">
                  <option value="all">All Sources</option>
                  <option value="user_input">User Input</option>
                  <option value="system">System</option>
                  <option value="agent">Agent</option>
                  <option value="dashboard">Dashboard</option>
                </select>
              </div>
            </div>
            <div class="card-content">
              <div class="memory-list" id="memory-list">
                <div class="memory-item">
                  <div class="memory-header">
                    <span class="memory-id">Loading memories...</span>
                    <span class="memory-source">—</span>
                  </div>
                  <div class="memory-content">Please wait while memories are loaded.</div>
                  <div class="memory-meta">
                    <span class="memory-time">—</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Search Tab -->
      <div class="tab-content" id="search-tab">
        <div class="search-container">
          <!-- Search Interface Card -->
          <div class="card search-card">
            <div class="card-header">
              <h3><i class="fas fa-search"></i> Semantic Search</h3>
            </div>
            <div class="card-content">
              <form id="search-form" class="search-form">
                <div class="search-input-group">
                  <input type="text" id="search-query" placeholder="Enter your search query..." required>
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Search
                  </button>
                </div>
                <div class="search-options">
                  <label class="checkbox-label">
                    <input type="checkbox" id="include-metadata">
                    <span class="checkmark"></span>
                    Include metadata in results
                  </label>
                  <div class="result-limit">
                    <label for="result-limit">Results:</label>
                    <select id="result-limit">
                      <option value="5">5</option>
                      <option value="10" selected>10</option>
                      <option value="20">20</option>
                      <option value="50">50</option>
                    </select>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- Search Results Card -->
          <div class="card results-card">
            <div class="card-header">
              <h3><i class="fas fa-list-ul"></i> Search Results</h3>
              <div class="results-info">
                <span id="results-count">0 results</span>
                <span id="search-time">—</span>
              </div>
            </div>
            <div class="card-content">
              <div class="search-results" id="search-results">
                <div class="no-results">
                  <i class="fas fa-search"></i>
                  <p>Enter a search query to find similar memories</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Agent Tab -->
      <div class="tab-content" id="agent-tab">
        <div class="agent-grid">
          <!-- Echo Status Card -->
          <div class="card echo-status-card">
            <div class="card-header">
              <h3><i class="fas fa-robot"></i> Echo Status</h3>
              <div class="echo-controls">
                <button class="btn btn-sm" onclick="triggerReflection()">
                  <i class="fas fa-brain"></i>
                  Trigger Reflection
                </button>
              </div>
            </div>
            <div class="card-content">
              <div class="echo-info">
                <div class="echo-avatar">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="echo-details">
                  <h4>Echo v1.2</h4>
                  <p>Alsania's AI Architect</p>
                  <div class="echo-stats">
                    <div class="stat">
                      <span class="stat-label">Status</span>
                      <span class="stat-value active" id="echo-current-status">Active</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Mode</span>
                      <span class="stat-value" id="echo-mode">Operational</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Last Activity</span>
                      <span class="stat-value" id="echo-last-activity">—</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Reflection Cycles Card -->
          <div class="card reflection-card">
            <div class="card-header">
              <h3><i class="fas fa-brain"></i> Reflection Cycles</h3>
            </div>
            <div class="card-content">
              <div class="reflection-timeline" id="reflection-timeline">
                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <span class="timeline-time">System startup</span>
                    <span class="timeline-text">Echo initialized</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Agent Metrics Card -->
          <div class="card agent-metrics-card">
            <div class="card-header">
              <h3><i class="fas fa-chart-bar"></i> Agent Metrics</h3>
            </div>
            <div class="card-content">
              <div class="metrics-display">
                <div class="metric-box">
                  <div class="metric-icon">
                    <i class="fas fa-memory"></i>
                  </div>
                  <div class="metric-data">
                    <span class="metric-value" id="agent-memory-usage">—</span>
                    <span class="metric-label">Memory Usage</span>
                  </div>
                </div>
                <div class="metric-box">
                  <div class="metric-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="metric-data">
                    <span class="metric-value" id="agent-response-time">—</span>
                    <span class="metric-label">Avg Response</span>
                  </div>
                </div>
                <div class="metric-box">
                  <div class="metric-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="metric-data">
                    <span class="metric-value" id="agent-queries">—</span>
                    <span class="metric-label">Queries Today</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scribe Tab -->
      <div class="tab-content" id="scribe-tab">
        <div class="scribe-container">
          <div class="card">
            <div class="card-header">
              <h3><i class="fas fa-feather-alt"></i> Scribe: Story Builder</h3>
            </div>
            <div class="card-content">
              <div class="controls">
                <button class="btn btn-primary" id="scribe-start-btn">Start</button>
                <button class="btn btn-secondary" id="scribe-stop-btn">Stop</button>
                <button class="btn btn-primary" id="scribe-generate-btn">Write New Chapter</button>
              </div>
              <div id="scribe-status" class="scribe-status">Status: Stopped</div>

              <h4>Chapters</h4>
              <ul id="chapter-list"></ul>

              <h4>Selected Chapter</h4>
              <textarea id="chapter-editor" rows="15"></textarea>
              <button class="btn btn-success" id="save-chapter-btn">Save Chapter</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Tab -->
      <div class="tab-content" id="security-tab">
        <div class="security-grid">
          <!-- Threat Detection Card -->
          <div class="card threat-card">
            <div class="card-header">
              <h3><i class="fas fa-shield-alt"></i> Threat Detection</h3>
              <div class="threat-status" id="threat-status">
                <div class="status-dot secure"></div>
                <span>Secure</span>
              </div>
            </div>
            <div class="card-content">
              <div class="threat-metrics">
                <div class="threat-item">
                  <span class="threat-label">Quarantined Items</span>
                  <span class="threat-value" id="quarantine-count">0</span>
                </div>
                <div class="threat-item">
                  <span class="threat-label">Blocked Attempts</span>
                  <span class="threat-value" id="blocked-attempts">0</span>
                </div>
                <div class="threat-item">
                  <span class="threat-label">Last Scan</span>
                  <span class="threat-value" id="last-scan">—</span>
                </div>
              </div>
              <button class="btn btn-secondary" onclick="runSecurityScan()">
                <i class="fas fa-search"></i>
                Run Security Scan
              </button>
            </div>
          </div>

          <!-- Forensic Logs Card -->
          <div class="card forensic-card">
            <div class="card-header">
              <h3><i class="fas fa-file-alt"></i> Forensic Logs</h3>
              <div class="log-controls">
                <select id="log-filter" onchange="filterLogs()">
                  <option value="all">All Events</option>
                  <option value="access">Access</option>
                  <option value="edit">Edit</option>
                  <option value="delete">Delete</option>
                  <option value="quarantine">Quarantine</option>
                </select>
                <button class="btn btn-sm" onclick="refreshLogs()">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>
            </div>
            <div class="card-content">
              <div class="log-viewer" id="log-viewer">
                <div class="log-entry">
                  <span class="log-time">Loading logs...</span>
                  <span class="log-type">—</span>
                  <span class="log-message">Please wait while logs are loaded.</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Backup & Recovery Card -->
          <div class="card backup-card">
            <div class="card-header">
              <h3><i class="fas fa-cloud-upload-alt"></i> Backup & Recovery</h3>
            </div>
            <div class="card-content">
              <div class="backup-status">
                <div class="backup-info">
                  <span class="backup-label">Last Backup</span>
                  <span class="backup-value" id="last-backup">—</span>
                </div>
                <div class="backup-info">
                  <span class="backup-label">Backup Size</span>
                  <span class="backup-value" id="backup-size">—</span>
                </div>
                <div class="backup-info">
                  <span class="backup-label">IPFS Status</span>
                  <span class="backup-value" id="ipfs-status">—</span>
                </div>
              </div>
              <div class="backup-controls">
                <button class="btn btn-primary" onclick="createBackup()">
                  <i class="fas fa-save"></i>
                  Create Backup
                </button>
                <button class="btn btn-secondary" onclick="listBackups()">
                  <i class="fas fa-list"></i>
                  List Backups
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Tab -->
      <div class="tab-content" id="system-tab">
        <div class="system-grid">
          <!-- System Information Card -->
          <div class="card system-info-card">
            <div class="card-header">
              <h3><i class="fas fa-info-circle"></i> System Information</h3>
            </div>
            <div class="card-content">
              <div class="system-details">
                <div class="detail-row">
                  <span class="detail-label">Version</span>
                  <span class="detail-value">AlsaniaMCP v1.0.0</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Uptime</span>
                  <span class="detail-value" id="system-uptime">—</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">API Token</span>
                  <span class="detail-value">
                    <span class="token-masked">••••••••••••</span>
                    <button class="btn btn-xs" onclick="toggleToken()">
                      <i class="fas fa-eye"></i>
                    </button>
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Environment</span>
                  <span class="detail-value" id="environment">Production</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Status Card -->
          <div class="card service-status-card">
            <div class="card-header">
              <h3><i class="fas fa-server"></i> Service Status</h3>
            </div>
            <div class="card-content">
              <div class="service-list">
                <div class="service-item">
                  <div class="service-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="service-info">
                    <span class="service-name">Qdrant Vector Store</span>
                    <span class="service-status" id="qdrant-service-status">—</span>
                  </div>
                  <div class="service-indicator" id="qdrant-indicator">
                    <div class="status-dot"></div>
                  </div>
                </div>
                <div class="service-item">
                  <div class="service-icon">
                    <i class="fas fa-table"></i>
                  </div>
                  <div class="service-info">
                    <span class="service-name">PostgreSQL Database</span>
                    <span class="service-status" id="postgres-service-status">—</span>
                  </div>
                  <div class="service-indicator" id="postgres-indicator">
                    <div class="status-dot"></div>
                  </div>
                </div>
                <div class="service-item">
                  <div class="service-icon">
                    <i class="fas fa-brain"></i>
                  </div>
                  <div class="service-info">
                    <span class="service-name">Embedding Service</span>
                    <span class="service-status" id="embedding-service-status">—</span>
                  </div>
                  <div class="service-indicator" id="embedding-indicator">
                    <div class="status-dot"></div>
                  </div>
                </div>
                <div class="service-item">
                  <div class="service-icon">
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <div class="service-info">
                    <span class="service-name">Sentinel Monitor</span>
                    <span class="service-status" id="sentinel-service-status">—</span>
                  </div>
                  <div class="service-indicator" id="sentinel-indicator">
                    <div class="status-dot"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Configuration Card -->
          <div class="card config-card">
            <div class="card-header">
              <h3><i class="fas fa-cogs"></i> Configuration</h3>
            </div>
            <div class="card-content">
              <div class="config-options">
                <div class="config-group">
                  <h4>Monitoring</h4>
                  <label class="switch">
                    <input type="checkbox" id="auto-refresh" checked>
                    <span class="slider"></span>
                    <span class="switch-label">Auto-refresh data</span>
                  </label>
                  <label class="switch">
                    <input type="checkbox" id="sound-alerts">
                    <span class="slider"></span>
                    <span class="switch-label">Sound alerts</span>
                  </label>
                </div>
                <div class="config-group">
                  <h4>Display</h4>
                  <label class="switch">
                    <input type="checkbox" id="dark-mode" checked>
                    <span class="slider"></span>
                    <span class="switch-label">Dark mode</span>
                  </label>
                  <label class="switch">
                    <input type="checkbox" id="animations" checked>
                    <span class="slider"></span>
                    <span class="switch-label">Animations</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-left">
        <span>© 2025 Alsania - Sovereign AI Ecosystem</span>
      </div>
      <div class="footer-center">
        <span>Echo v1.2 | MCP v1.0.0</span>
      </div>
      <div class="footer-right">
        <span>Hardened • Relentless • Sovereign</span>
      </div>
    </footer>
  </div>

  <!-- Notification System -->
  <div id="notification-container" class="notification-container"></div>

  <!-- Modal for detailed views -->
  <div id="modal" class="modal hidden">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">Modal Title</h3>
        <button class="modal-close" onclick="closeModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body" id="modal-body">
        Modal content goes here
      </div>
    </div>
  <main>
  <script src="/js/mcp.js"></script>  
</body>
</html>