/* ===== ALSANIA MCP DASHBOARD STYLES ===== */

/* CSS Variables for Alsania Brand Colors */
:root {
  /* Primary Alsania Colors */
  --alsania-primary: #39FF14;
  --alsania-secondary: #00ccff;
  --alsania-accent: #ff6b35;
  --alsania-warning: #ffaa00;
  --alsania-danger: #ff4757;

  /* Background Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-card: #1e1e1e;
  --bg-hover: #333333;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;
  --text-accent: var(--alsania-primary);

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #444444;
  --border-accent: var(--alsania-primary);

  /* Status Colors */
  --status-healthy: #39FF14;
  --status-warning: #ffaa00;
  --status-error: #ff4757;
  --status-offline: #666666;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 20px rgba(0, 255, 136, 0.3);

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== GLOBAL STYLES ===== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rajdhani', 'Arial', sans-serif;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><radialGradient id="bg" cx="50%" cy="50%"><stop offset="0%" style="stop-color:%23001a1a"/><stop offset="100%" style="stop-color:%23000000"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23bg)"/><circle cx="200" cy="200" r="2" fill="%2339FF14" opacity="0.3"/><circle cx="800" cy="150" r="1" fill="%2339FF14" opacity="0.5"/><circle cx="1200" cy="300" r="1.5" fill="%2339FF14" opacity="0.4"/><circle cx="1600" cy="200" r="2" fill="%2339FF14" opacity="0.3"/><circle cx="400" cy="600" r="1" fill="%2339FF14" opacity="0.6"/><circle cx="1000" cy="700" r="1.5" fill="%2339FF14" opacity="0.4"/><circle cx="1400" cy="800" r="2" fill="%2339FF14" opacity="0.3"/></svg>') no-repeat center center fixed;
  background-size: cover;
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* ===== UTILITY CLASSES ===== */

.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-muted);
}

.text-accent {
  color: var(--text-accent);
}

/* ===== LOADING SCREEN ===== */

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  animation: pulse 2s infinite;
}

.alsania-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.alsania-logo i {
  font-size: 3rem;
  color: var(--alsania-primary);
  animation: glow 2s ease-in-out infinite alternate;
}

.alsania-logo span {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--text-primary);
  letter-spacing: 3px;
}

.loading-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.loading-bar {
  width: 300px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--alsania-primary), var(--alsania-secondary));
  border-radius: 2px;
  animation: loading 3s ease-in-out infinite;
}

/* ===== HEADER ===== */

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: var(--bg-secondary);
  border-bottom: 2px solid var(--border-accent);
  box-shadow: var(--shadow-md);
}

.header-left .alsania-brand {
  display: flex;
  align-items: center;
  gap: 15px;
}

.brand-icon {
  font-size: 2.5rem;
  color: var(--alsania-primary);
  animation: glow 3s ease-in-out infinite alternate;
}

.brand-text h1 {
  font-family: 'Orbitron', monospace;
  font-size: 2rem;
  font-weight: 900;
  color: var(--text-primary);
  letter-spacing: 2px;
}

.mcp-text {
  color: var(--alsania-primary);
  animation: glow 3s ease-in-out infinite alternate;
}

.tagline {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 300;
  letter-spacing: 1px;
  animation: electrical 3s ease-in-out infinite alternate;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 20px;
}

.system-time {
  font-family: 'Orbitron', monospace;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--status-healthy);
}

.header-right .echo-status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  background: var(--bg-tertiary);
  border-radius: 20px;
  border: 1px solid var(--border-secondary);
}

.echo-status i {
  color: var(--alsania-primary);
  animation: pulse 2s infinite;
}

/* ===== NAVIGATION ===== */

.nav-tabs {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
  white-space: nowrap;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 25px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 3px solid transparent;
  font-size: 0.95rem;
  font-weight: 500;
}

.nav-tab:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-tab.active {
  color: var(--alsania-primary);
  border-bottom-color: var(--alsania-primary);
  background: var(--bg-tertiary);
}

.nav-tab i {
  font-size: 1.1rem;
}

/* ===== MAIN CONTENT ===== */

.main-content {
  padding: 30px;
  min-height: calc(100vh - 200px);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

/* ===== DASHBOARD GRIDS ===== */

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.memory-grid,
.search-container,
.agent-grid,
.security-grid,
.system-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.search-container {
  grid-template-columns: 1fr;
  gap: 20px;
}

/* ===== CARDS ===== */

.card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header h3 i {
  color: var(--alsania-primary);
}

.card-content {
  padding: 25px;
}

/* ===== BUTTONS ===== */

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--alsania-primary);
  color: var(--bg-primary);
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.btn:hover {
  background: var(--alsania-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.btn-secondary:hover {
  background: var(--bg-hover);
  border-color: var(--alsania-primary);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-xs {
  padding: 4px 8px;
  font-size: 0.7rem;
}

.controls button {
  margin-right: 5px;
}

/* ===== FORMS ===== */

#chapter-list {
  list-style: none;
  padding-left: 0;
  margin-top: 10px;
}

#chapter-list li {
  cursor: pointer;
  padding: 5px;
  border-bottom: 1px solid #39ff14;
}

#chapter-list li:hover {
  background: #222;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-secondary);
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  width: 100%;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all var(--transition-fast);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--alsania-primary);
  box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* ===== STATUS INDICATORS ===== */

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--status-offline);
  animation: pulse 2s infinite;
}

.status-dot.healthy {
  background: var(--status-healthy);
}

.status-dot.warning {
  background: var(--status-warning);
}

.status-dot.error {
  background: var(--status-error);
}

.status-dot.secure {
  background: var(--status-healthy);
}

.pulse-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--alsania-primary);
  animation: pulse 1.5s infinite;
}

/* ===== HEALTH INDICATORS ===== */

.health-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: var(--bg-tertiary);
  border-radius: 20px;
  font-size: 0.85rem;
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-primary);
}

.metric:last-child {
  border-bottom: none;
}

.metric-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.metric-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* ===== PERFORMANCE METRICS ===== */

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.metric-icon.cpu {
  background: rgba(0, 255, 136, 0.1);
  color: var(--alsania-primary);
}

.metric-icon.memory {
  background: rgba(0, 204, 255, 0.1);
  color: var(--alsania-secondary);
}

.metric-icon.disk {
  background: rgba(255, 107, 53, 0.1);
  color: var(--alsania-accent);
}

.metric-info {
  flex: 1;
}

.metric-info .metric-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-muted);
}

.metric-info .metric-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* ===== AGENT STATUS ===== */

.agent-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: var(--bg-tertiary);
  border-radius: 20px;
  font-size: 0.85rem;
}

.agent-stats {
  display: grid;
  gap: 15px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-primary);
}

.stat:last-child {
  border-bottom: none;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
}

.stat-value.active {
  color: var(--status-healthy);
}

/* ===== ACTIVITY FEED ===== */

.activity-feed {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid var(--border-primary);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--alsania-primary);
  font-size: 0.9rem;
}

.activity-content {
  flex: 1;
}

.activity-text {
  display: block;
  color: var(--text-primary);
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.activity-time {
  display: block;
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* ===== MEMORY COMPONENTS ===== */

.store-form {
  display: grid;
  gap: 20px;
}

.memory-list {
  max-height: 400px;
  overflow-y: auto;
}

.memory-item {
  padding: 15px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all var(--transition-fast);
}

.memory-item:hover {
  border-color: var(--alsania-primary);
  background: var(--bg-hover);
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.memory-id {
  font-family: 'Orbitron', monospace;
  font-size: 0.8rem;
  color: var(--alsania-primary);
}

.memory-source {
  padding: 2px 8px;
  background: var(--bg-primary);
  border-radius: 12px;
  font-size: 0.7rem;
  color: var(--text-secondary);
}

.memory-content {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 10px;
}

.memory-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* ===== SEARCH COMPONENTS ===== */

.search-form {
  display: grid;
  gap: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
}

.search-input-group input {
  flex: 1;
}

.search-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.result-limit {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.result-limit select {
  width: auto;
  padding: 6px 10px;
}

.results-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.85rem;
  color: var(--text-muted);
}

.search-results {
  max-height: 500px;
  overflow-y: auto;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
}

.no-results i {
  font-size: 2rem;
  margin-bottom: 15px;
  color: var(--alsania-primary);
}

.search-result-item {
  padding: 15px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all var(--transition-fast);
}

.search-result-item:hover {
  border-color: var(--alsania-primary);
  background: var(--bg-hover);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-score {
  padding: 2px 8px;
  background: var(--alsania-primary);
  color: var(--bg-primary);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.result-content {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 10px;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* ===== FOOTER ===== */

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  font-size: 0.85rem;
  color: var(--text-muted);
}

.footer-center {
  font-family: 'Orbitron', monospace;
  color: var(--alsania-primary);
}

/* ===== NOTIFICATIONS ===== */

.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  padding: 15px 20px;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left: 4px solid var(--status-healthy);
}

.notification.warning {
  border-left: 4px solid var(--status-warning);
}

.notification.error {
  border-left: 4px solid var(--status-error);
}

/* ===== MODAL ===== */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

/* ===== SWITCHES ===== */

.switch {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  cursor: pointer;
}

.switch input[type="checkbox"] {
  display: none;
}

.slider {
  width: 40px;
  height: 20px;
  background: var(--bg-tertiary);
  border-radius: 20px;
  position: relative;
  transition: all var(--transition-fast);
}

.slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: var(--text-muted);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.switch input:checked + .slider {
  background: var(--alsania-primary);
}

.switch input:checked + .slider::before {
  transform: translateX(20px);
  background: var(--bg-primary);
}

.switch-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* ===== ANIMATIONS ===== */

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px var(--alsania-primary);
  }
  100% {
    text-shadow: 0 0 20px var(--alsania-primary), 0 0 30px var(--alsania-primary);
  }
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1200px) {
  .dashboard-grid,
  .memory-grid,
  .agent-grid,
  .security-grid,
  .system-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-right {
    justify-content: center;
  }

  .nav-tabs {
    justify-content: center;
  }

  .nav-tab {
    padding: 12px 15px;
    font-size: 0.85rem;
  }

  .main-content {
    padding: 20px 15px;
  }

  .dashboard-grid,
  .memory-grid,
  .agent-grid,
  .security-grid,
  .system-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-header {
    padding: 15px 20px;
  }

  .card-content {
    padding: 20px;
  }

  .footer {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .brand-text h1 {
    font-size: 1.5rem;
  }

  .nav-tab {
    padding: 10px 12px;
  }

  .nav-tab span {
    display: none;
  }

  .main-content {
    padding: 15px 10px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}

/* ===== SCROLLBAR STYLING ===== */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--alsania-primary);
}