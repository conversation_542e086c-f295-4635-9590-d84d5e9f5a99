// ===== ALSANIA MCP DASHBOARD JAVASCRIPT =====

// Global state management
const AppState = {
  isConnected: true,
  currentTab: 'overview',
  autoRefresh: true,
  refreshInterval: null,
  apiToken: localStorage.getItem('alsania_api_token') || 'alsania-dev',
  notifications: [],
  systemStartTime: Date.now()
};

// ===== INITIALIZATION =====

document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
});

async function initializeApp() {
  showLoadingScreen();

  try {
    // Initialize components
    await initializeAuth();
    initializeNavigation();
    initializeEventListeners();
    initializeAutoRefresh();

    // Load initial data
    await loadInitialData();

    // Hide loading screen and show dashboard
    setTimeout(() => {
      hideLoadingScreen();
      showNotification('AlsaniaMCP initialized successfully', 'success');
    }, 2000);

  } catch (error) {
    console.error('Failed to initialize app:', error);
    showNotification('Failed to initialize AlsaniaMCP', 'error');
    hideLoadingScreen();
  }
}

function showLoadingScreen() {
  document.getElementById('loading-screen').classList.remove('hidden');
  document.getElementById('dashboard').classList.add('hidden');
}

function hideLoadingScreen() {
  document.getElementById('loading-screen').classList.add('hidden');
  document.getElementById('dashboard').classList.remove('hidden');
}

// ===== AUTHENTICATION =====

async function initializeAuth() {
  // For now, we'll use a simple token-based auth
  // In production, implement proper OAuth or JWT
  if (!AppState.apiToken) {
    AppState.apiToken = prompt('Enter API Token:') || 'alsania-dev';
    localStorage.setItem('alsania_api_token', AppState.apiToken);
  }
}

function getAuthHeaders() {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AppState.apiToken}`
  };
}

// ===== NAVIGATION =====

function initializeNavigation() {
  const navTabs = document.querySelectorAll('.nav-tab');

  navTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const tabName = tab.dataset.tab;
      switchTab(tabName);
    });
  });
}

function switchTab(tabName) {
  // Update nav tabs
  document.querySelectorAll('.nav-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

  // Update tab content
  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.remove('active');
  });
  document.getElementById(`${tabName}-tab`).classList.add('active');

  AppState.currentTab = tabName;

  // Load tab-specific data
  loadTabData(tabName);
}

async function loadTabData(tabName) {
  switch (tabName) {
    case 'overview':
      await loadOverviewData();
      break;
    case 'memory':
      await loadMemoryData();
      break;
    case 'search':
      // Search tab loads data on demand
      break;
    case 'agent':
      await loadAgentData();
      break;
    case 'security':
      await loadSecurityData();
      break;
    case 'system':
      await loadSystemData();
      break;
  }
}

// ===== EVENT LISTENERS =====

function initializeEventListeners() {
  // System time update
  updateSystemTime();
  setInterval(updateSystemTime, 1000);

  // Form submissions
  const storeForm = document.getElementById('store-form');
  if (storeForm) {
    storeForm.addEventListener('submit', handleStoreMemory);
  }

  const searchForm = document.getElementById('search-form');
  if (searchForm) {
    searchForm.addEventListener('submit', handleSearch);
  }

  // Configuration changes
  const autoRefreshToggle = document.getElementById('auto-refresh');
  if (autoRefreshToggle) {
    autoRefreshToggle.addEventListener('change', toggleAutoRefresh);
  }
}

function updateSystemTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  const timeElement = document.getElementById('system-time');
  if (timeElement) {
    timeElement.textContent = timeString;
  }
}

// ===== AUTO REFRESH =====

function initializeAutoRefresh() {
  if (AppState.autoRefresh) {
    startAutoRefresh();
  }
}

function startAutoRefresh() {
  if (AppState.refreshInterval) {
    clearInterval(AppState.refreshInterval);
  }

  AppState.refreshInterval = setInterval(async () => {
    if (AppState.currentTab === 'overview') {
      await refreshOverviewData();
    }
  }, 5000); // Refresh every 5 seconds
}

function stopAutoRefresh() {
  if (AppState.refreshInterval) {
    clearInterval(AppState.refreshInterval);
    AppState.refreshInterval = null;
  }
}

function toggleAutoRefresh() {
  const toggle = document.getElementById('auto-refresh');
  AppState.autoRefresh = toggle.checked;

  if (AppState.autoRefresh) {
    startAutoRefresh();
    showNotification('Auto-refresh enabled', 'success');
  } else {
    stopAutoRefresh();
    showNotification('Auto-refresh disabled', 'warning');
  }
}

// ===== DATA LOADING FUNCTIONS =====

async function loadInitialData() {
  await loadOverviewData();
  addActivityItem('System initialized', 'fas fa-power-off');
}

async function loadOverviewData() {
  try {
    await Promise.all([
      loadHealthStatus(),
      loadPerformanceMetrics(),
      loadAgentStatus()
    ]);
  } catch (error) {
    console.error('Failed to load overview data:', error);
    showNotification('Failed to load system data', 'error');
  }
}

async function refreshOverviewData() {
  await loadOverviewData();
}

async function loadHealthStatus() {
  try {
    const response = await fetch('/health', {
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const health = await response.json();
    updateHealthDisplay(health);

  } catch (error) {
    console.error('Failed to load health status:', error);
    updateHealthDisplay({ status: 'error', services: {} });
  }
}

function updateHealthDisplay(health) {
  const indicator = document.getElementById('health-indicator');
  const statusText = document.getElementById('health-status-text');
  const statusDot = indicator.querySelector('.status-dot');

  // Update main status
  statusText.textContent = health.status || 'Unknown';

  // Update status dot
  statusDot.className = 'status-dot';
  if (health.status === 'healthy') {
    statusDot.classList.add('healthy');
  } else if (health.status === 'degraded') {
    statusDot.classList.add('warning');
  } else {
    statusDot.classList.add('error');
  }

  // Update individual services
  const services = health.services || {};
  updateServiceStatus('qdrant-status', services.qdrant);
  updateServiceStatus('postgres-status', services.postgresql);
  updateServiceStatus('embedding-status', services.embedding);
}

function updateServiceStatus(elementId, status) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = status || 'Unknown';
    element.className = 'metric-value';

    if (status === 'healthy' || status === 'configured') {
      element.style.color = 'var(--status-healthy)';
    } else if (status === 'not_configured') {
      element.style.color = 'var(--status-warning)';
    } else {
      element.style.color = 'var(--status-error)';
    }
  }
}

async function loadPerformanceMetrics() {
  try {
    const [cpuRes, memRes, diskRes] = await Promise.all([
      fetch('/metrics/cpu', { headers: getAuthHeaders() }),
      fetch('/metrics/memory', { headers: getAuthHeaders() }),
      fetch('/metrics/disk', { headers: getAuthHeaders() })
    ]);

    const cpu = await cpuRes.json();
    const memory = await memRes.json();
    const disk = await diskRes.json();

    updateMetricDisplay('cpu-usage', `${cpu.cpu_percent?.toFixed(1) || 0}%`);
    updateMetricDisplay('memory-usage', `${memory.percent?.toFixed(1) || 0}%`);
    updateMetricDisplay('disk-usage', `${disk.percent?.toFixed(1) || 0}%`);

  } catch (error) {
    console.error('Failed to load performance metrics:', error);
    updateMetricDisplay('cpu-usage', '—');
    updateMetricDisplay('memory-usage', '—');
    updateMetricDisplay('disk-usage', '—');
  }
}

function updateMetricDisplay(elementId, value) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = value;
  }
}

async function loadAgentStatus() {
  // Update Echo status
  const echoStatus = document.getElementById('echo-state');
  const agentStatus = document.getElementById('echo-current-status');
  const lastActivity = document.getElementById('echo-last-activity');
  const uptime = document.getElementById('agent-uptime');

  if (echoStatus) echoStatus.textContent = 'Active';
  if (agentStatus) agentStatus.textContent = 'Operational';
  if (lastActivity) lastActivity.textContent = 'Just now';
  if (uptime) {
    const uptimeMs = Date.now() - AppState.systemStartTime;
    const uptimeMinutes = Math.floor(uptimeMs / 60000);
    uptime.textContent = `${uptimeMinutes}m`;
  }
}

// ===== MEMORY MANAGEMENT =====

async function loadMemoryData() {
  // For now, show placeholder data
  // In a real implementation, this would fetch from a /memories endpoint
  showNotification('Memory data loaded', 'success');
}

async function handleStoreMemory(event) {
  event.preventDefault();

  const text = document.getElementById('memory-text').value;
  const source = document.getElementById('memory-source').value;

  if (!text.trim()) {
    showNotification('Memory content is required', 'error');
    return;
  }

  try {
    const response = await fetch('/store', {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ text, source })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const result = await response.json();
    showNotification(`Memory stored: ${result.mem_id}`, 'success');
    addActivityItem(`Stored memory from ${source}`, 'fas fa-save');

    // Clear form
    document.getElementById('memory-text').value = '';

  } catch (error) {
    console.error('Failed to store memory:', error);
    showNotification('Failed to store memory', 'error');
  }
}

// ===== SEARCH FUNCTIONALITY =====

async function handleSearch(event) {
  event.preventDefault();

  const query = document.getElementById('search-query').value;
  const includeMetadata = document.getElementById('include-metadata').checked;
  const resultLimit = document.getElementById('result-limit').value;

  if (!query.trim()) {
    showNotification('Search query is required', 'error');
    return;
  }

  const startTime = Date.now();

  try {
    const response = await fetch('/ask', {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ query })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const result = await response.json();
    const searchTime = Date.now() - startTime;

    displaySearchResults(result.results || [], query, searchTime, includeMetadata);
    addActivityItem(`Searched for: "${query}"`, 'fas fa-search');

  } catch (error) {
    console.error('Failed to search:', error);
    showNotification('Search failed', 'error');
    displaySearchResults([], query, 0, false);
  }
}

function displaySearchResults(results, query, searchTime, includeMetadata) {
  const resultsContainer = document.getElementById('search-results');
  const resultsCount = document.getElementById('results-count');
  const searchTimeElement = document.getElementById('search-time');

  // Update info
  resultsCount.textContent = `${results.length} results`;
  searchTimeElement.textContent = `${searchTime}ms`;

  // Clear previous results
  resultsContainer.innerHTML = '';

  if (results.length === 0) {
    resultsContainer.innerHTML = `
      <div class="no-results">
        <i class="fas fa-search"></i>
        <p>No results found for "${query}"</p>
      </div>
    `;
    return;
  }

  // Display results
  results.forEach((result, index) => {
    const resultElement = createSearchResultElement(result, index, includeMetadata);
    resultsContainer.appendChild(resultElement);
  });
}

function createSearchResultElement(result, index, includeMetadata) {
  const div = document.createElement('div');
  div.className = 'search-result-item';

  const score = result.score || Math.random() * 0.5 + 0.5; // Fallback score
  const text = result.text || result.memory_id || 'No content available';
  const source = result.source || 'unknown';

  div.innerHTML = `
    <div class="result-header">
      <span class="memory-id">#${index + 1}</span>
      <span class="result-score">${(score * 100).toFixed(1)}%</span>
    </div>
    <div class="result-content">${text}</div>
    <div class="result-meta">
      <span>Source: ${source}</span>
      ${includeMetadata ? `<span>ID: ${result.memory_id || 'N/A'}</span>` : ''}
    </div>
  `;

  return div;
}

// ===== AGENT MANAGEMENT =====

async function loadAgentData() {
  await loadAgentStatus();
  loadReflectionTimeline();
}

function loadReflectionTimeline() {
  const timeline = document.getElementById('reflection-timeline');
  if (timeline) {
    // Add some sample reflection entries
    const reflections = [
      { time: 'System startup', text: 'Echo initialized' },
      { time: '5 min ago', text: 'Memory integrity check completed' },
      { time: '15 min ago', text: 'Vector store optimization' }
    ];

    timeline.innerHTML = reflections.map(reflection => `
      <div class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-content">
          <span class="timeline-time">${reflection.time}</span>
          <span class="timeline-text">${reflection.text}</span>
        </div>
      </div>
    `).join('');
  }
}

async function triggerReflection() {
  showNotification('Triggering Echo reflection cycle...', 'success');
  addActivityItem('Manual reflection triggered', 'fas fa-brain');

  // Add new reflection to timeline
  const timeline = document.getElementById('reflection-timeline');
  if (timeline) {
    const newItem = document.createElement('div');
    newItem.className = 'timeline-item';
    newItem.innerHTML = `
      <div class="timeline-dot"></div>
      <div class="timeline-content">
        <span class="timeline-time">Just now</span>
        <span class="timeline-text">Manual reflection cycle initiated</span>
      </div>
    `;
    timeline.insertBefore(newItem, timeline.firstChild);
  }
}

// === SCRIBE TAB ===
const scribeStatus = document.getElementById("scribe-status");
const chapterList = document.getElementById("chapter-list");
const chapterEditor = document.getElementById("chapter-editor");

async function refreshScribeStatus() {
  const res = await fetch("/scribe/status");
  const data = await res.json();
  scribeStatus.innerText = `Status: ${data.scribe}`;
}

async function loadChapters() {
  const res = await fetch("/scribe/get_chapters");
  const data = await res.json();
  chapterList.innerHTML = "";

  data.chapters.forEach((chapter, idx) => {
    const li = document.createElement("li");
    li.innerText = `Chapter ${idx + 1}: ${chapter.title}`;
    li.addEventListener("click", () => {
      chapterEditor.value = chapter.content;
      chapterEditor.dataset.index = idx;
    });
    chapterList.appendChild(li);
  });
}

document.getElementById("scribe-start-btn").addEventListener("click", async () => {
  await fetch("/scribe/start", { method: "POST" });
  refreshScribeStatus();
});

document.getElementById("scribe-stop-btn").addEventListener("click", async () => {
  await fetch("/scribe/stop", { method: "POST" });
  refreshScribeStatus();
});

document.getElementById("scribe-generate-btn").addEventListener("click", async () => {
  await fetch("/scribe/write", { method: "POST" });
  alert("Scribe is generating a new chapter...");
  loadChapters();
});

document.getElementById("save-chapter-btn").addEventListener("click", async () => {
  const idx = chapterEditor.dataset.index;
  const content = chapterEditor.value;
  if (idx === undefined) {
    alert("Please select a chapter to save.");
    return;
  }

  await fetch("/scribe/edit_chapter", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ index: idx, content })
  });
  alert("Chapter saved.");
  loadChapters();
});

// === Init ===
refreshScribeStatus();
loadAgents();
loadChapters();

// ===== SECURITY FUNCTIONS =====

async function loadSecurityData() {
  await loadQuarantineLogs();
  updateSecurityMetrics();
}

async function loadQuarantineLogs() {
  try {
    const response = await fetch('/metrics/quarantine_log', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const data = await response.json();
      displayQuarantineLogs(data.entries || []);
    }
  } catch (error) {
    console.error('Failed to load quarantine logs:', error);
  }
}

function displayQuarantineLogs(logs) {
  const logViewer = document.getElementById('log-viewer');
  if (!logViewer) return;

  if (logs.length === 0) {
    logViewer.innerHTML = `
      <div class="log-entry">
        <span class="log-time">No quarantine events</span>
        <span class="log-type">INFO</span>
        <span class="log-message">System is secure</span>
      </div>
    `;
    return;
  }

  logViewer.innerHTML = logs.map(log => `
    <div class="log-entry">
      <span class="log-time">${new Date(log.timestamp).toLocaleTimeString()}</span>
      <span class="log-type">${log.type || 'INFO'}</span>
      <span class="log-message">${log.message || 'Quarantine event'}</span>
    </div>
  `).join('');
}

function updateSecurityMetrics() {
  // Update security metrics with placeholder data
  updateMetricDisplay('quarantine-count', '0');
  updateMetricDisplay('blocked-attempts', '0');
  updateMetricDisplay('last-scan', 'Just now');
}

async function runSecurityScan() {
  showNotification('Running security scan...', 'success');
  addActivityItem('Security scan initiated', 'fas fa-shield-alt');

  // Simulate scan
  setTimeout(() => {
    showNotification('Security scan completed - No threats detected', 'success');
    updateMetricDisplay('last-scan', 'Just now');
  }, 2000);
}

// ===== SYSTEM MANAGEMENT =====

async function loadSystemData() {
  updateSystemInfo();
  await loadServiceStatus();
}

function updateSystemInfo() {
  const uptimeElement = document.getElementById('system-uptime');
  if (uptimeElement) {
    const uptimeMs = Date.now() - AppState.systemStartTime;
    const uptimeHours = Math.floor(uptimeMs / 3600000);
    const uptimeMinutes = Math.floor((uptimeMs % 3600000) / 60000);
    uptimeElement.textContent = `${uptimeHours}h ${uptimeMinutes}m`;
  }
}

async function loadServiceStatus() {
  try {
    const response = await fetch('/health', {
      headers: getAuthHeaders()
    });

    if (response.ok) {
      const health = await response.json();
      updateServiceIndicators(health.services || {});
    }
  } catch (error) {
    console.error('Failed to load service status:', error);
  }
}

function updateServiceIndicators(services) {
  updateServiceIndicator('qdrant', services.qdrant);
  updateServiceIndicator('postgres', services.postgresql);
  updateServiceIndicator('embedding', services.embedding);
  updateServiceIndicator('sentinel', 'healthy'); // Assume healthy for now
}

function updateServiceIndicator(serviceName, status) {
  const statusElement = document.getElementById(`${serviceName}-service-status`);
  const indicatorElement = document.getElementById(`${serviceName}-indicator`);

  if (statusElement) {
    statusElement.textContent = status || 'Unknown';
  }

  if (indicatorElement) {
    const dot = indicatorElement.querySelector('.status-dot');
    if (dot) {
      dot.className = 'status-dot';
      if (status === 'healthy' || status === 'configured') {
        dot.classList.add('healthy');
      } else if (status === 'not_configured') {
        dot.classList.add('warning');
      } else {
        dot.classList.add('error');
      }
    }
  }
}

// ===== BACKUP MANAGEMENT =====

async function createBackup() {
  showNotification('Creating backup...', 'success');
  addActivityItem('Backup initiated', 'fas fa-cloud-upload-alt');

  try {
    // Note: This endpoint doesn't exist yet, so we'll simulate it
    // const response = await fetch('/backup/create', {
    //   method: 'POST',
    //   headers: getAuthHeaders()
    // });

    // Simulate backup creation
    setTimeout(() => {
      showNotification('Backup created successfully', 'success');
      updateMetricDisplay('last-backup', 'Just now');
      updateMetricDisplay('backup-size', '2.4 MB');
      updateMetricDisplay('ipfs-status', 'Connected');
    }, 3000);

  } catch (error) {
    console.error('Failed to create backup:', error);
    showNotification('Backup failed', 'error');
  }
}

async function listBackups() {
  showNotification('Loading backup list...', 'success');

  // Simulate backup list
  setTimeout(() => {
    const backups = [
      { id: 'backup_001', date: '2024-01-15 10:30', size: '2.4 MB' },
      { id: 'backup_002', date: '2024-01-14 10:30', size: '2.3 MB' },
      { id: 'backup_003', date: '2024-01-13 10:30', size: '2.2 MB' }
    ];

    showModal('Backup List', `
      <div class="backup-list">
        ${backups.map(backup => `
          <div class="backup-item">
            <strong>${backup.id}</strong><br>
            <small>${backup.date} - ${backup.size}</small>
          </div>
        `).join('')}
      </div>
    `);
  }, 1000);
}

// ===== UTILITY FUNCTIONS =====

function refreshMetrics() {
  if (AppState.currentTab === 'overview') {
    loadPerformanceMetrics();
  }
  showNotification('Metrics refreshed', 'success');
}

function clearActivity() {
  const activityFeed = document.getElementById('activity-feed');
  if (activityFeed) {
    activityFeed.innerHTML = `
      <div class="activity-item">
        <div class="activity-icon">
          <i class="fas fa-broom"></i>
        </div>
        <div class="activity-content">
          <span class="activity-text">Activity feed cleared</span>
          <span class="activity-time">Just now</span>
        </div>
      </div>
    `;
  }
}

function addActivityItem(text, iconClass) {
  const activityFeed = document.getElementById('activity-feed');
  if (!activityFeed) return;

  const newItem = document.createElement('div');
  newItem.className = 'activity-item';
  newItem.innerHTML = `
    <div class="activity-icon">
      <i class="${iconClass}"></i>
    </div>
    <div class="activity-content">
      <span class="activity-text">${text}</span>
      <span class="activity-time">Just now</span>
    </div>
  `;

  // Insert at the beginning
  activityFeed.insertBefore(newItem, activityFeed.firstChild);

  // Keep only the last 10 items
  const items = activityFeed.querySelectorAll('.activity-item');
  if (items.length > 10) {
    items[items.length - 1].remove();
  }
}

// ===== NOTIFICATION SYSTEM =====

function showNotification(message, type = 'success') {
  const container = document.getElementById('notification-container');
  if (!container) return;

  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div style="display: flex; align-items: center; gap: 10px;">
      <i class="fas ${getNotificationIcon(type)}"></i>
      <span>${message}</span>
    </div>
  `;

  container.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 5000);

  // Add to state
  AppState.notifications.push({
    message,
    type,
    timestamp: Date.now()
  });
}

function getNotificationIcon(type) {
  switch (type) {
    case 'success': return 'fa-check-circle';
    case 'warning': return 'fa-exclamation-triangle';
    case 'error': return 'fa-times-circle';
    default: return 'fa-info-circle';
  }
}

// ===== MODAL SYSTEM =====

function showModal(title, content) {
  const modal = document.getElementById('modal');
  const modalTitle = document.getElementById('modal-title');
  const modalBody = document.getElementById('modal-body');

  if (modal && modalTitle && modalBody) {
    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    modal.classList.remove('hidden');
  }
}

function closeModal() {
  const modal = document.getElementById('modal');
  if (modal) {
    modal.classList.add('hidden');
  }
}

// ===== LEGACY FUNCTION COMPATIBILITY =====

// Keep these for backward compatibility with existing HTML
async function saveSnapshot() {
  await createBackup();
}

async function listSnapshots() {
  await listBackups();
}

async function searchVector() {
  const query = document.getElementById('searchInput')?.value ||
                document.getElementById('search-query')?.value;
  if (query) {
    document.getElementById('search-query').value = query;
    await handleSearch(new Event('submit'));
  }
}

async function checkHealth() {
  await loadHealthStatus();
  showNotification('Health check completed', 'success');
}

async function triggerBackup() {
  await createBackup();
}

// ===== KEYBOARD SHORTCUTS =====

document.addEventListener('keydown', function(event) {
  // Ctrl/Cmd + K for search
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault();
    switchTab('search');
    setTimeout(() => {
      const searchInput = document.getElementById('search-query');
      if (searchInput) searchInput.focus();
    }, 100);
  }

  // Escape to close modal
  if (event.key === 'Escape') {
    closeModal();
  }
});

// ===== ERROR HANDLING =====

window.addEventListener('error', function(event) {
  console.error('Global error:', event.error);
  showNotification('An unexpected error occurred', 'error');
});

window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
  showNotification('A network error occurred', 'error');
});