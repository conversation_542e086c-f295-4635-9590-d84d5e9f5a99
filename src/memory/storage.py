# memory/storage.py

import os
import json
import uuid
from datetime import datetime

SNAPSHOT_DIR = "snapshots"

def ensure_dir():
    if not os.path.exists(SNAPSHOT_DIR):
        os.makedirs(SNAPSHOT_DIR)

def save_snapshot(data: dict, name: str = None) -> str:
    ensure_dir()
    snapshot_id = name or str(uuid.uuid4())
    timestamp = datetime.utcnow().isoformat()
    path = os.path.join(SNAPSHOT_DIR, f"{snapshot_id}.json")
    with open(path, "w") as f:
        json.dump({"id": snapshot_id, "timestamp": timestamp, "data": data}, f, indent=2)
    return snapshot_id

def load_snapshot(snapshot_id: str) -> dict:
    path = os.path.join(SNAPSHOT_DIR, f"{snapshot_id}.json")
    if not os.path.exists(path):
        raise FileNotFoundError(f"Snapshot {snapshot_id} not found.")
    with open(path, "r") as f:
        return json.load(f)

def list_snapshots() -> list:
    ensure_dir()
    return [f[:-5] for f in os.listdir(SNAPSHOT_DIR) if f.endswith(".json")]

def delete_snapshot(snapshot_id: str):
    path = os.path.join(SNAPSHOT_DIR, f"{snapshot_id}.json")
    if os.path.exists(path):
        os.remove(path)
