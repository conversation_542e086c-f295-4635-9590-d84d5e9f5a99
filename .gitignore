# Ignore node_modules folder
/node_modules/
# Ignore build folder
/build/
# Ignore dist folder
/dist/
# Ignore coverage folder
/coverage/
# Ignore logs folder
/logs/
# Ignore temp folder
/temp/
# Ignore tmp folder
/tmp/
# Ignore cache folder
/cache/
# Ignore log files
*.log
# Ignore temporary files created by some editors like Sublime Text etc.
*~
# Ignore backup files created by some editors like Vim and Emacs.
.*.swp
# Ignore backup files created by Visual Studio Code.
.vscode/*
# Ignore any file in the root directory with a name starting with dot (.) except .gitignore itself.
!.gitignore
# Ignore all .env files
.env
/.env*
# Ignore all JSON config files
config.json
/config.json*
# Ignore all lockfiles for npm packages
package-lock.json
/npm-shrinkwrap.json
/yarn.lock
/package-lock.json*
/npm-shrinkwrap.json*
/yarn.lock*